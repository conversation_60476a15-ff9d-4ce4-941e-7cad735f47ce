import os
from threading import local
import paramiko
# import leveldb
import plyvel
import datetime
import json
import pandas as pd
import os
from data import data_reader
from utils import trading_calendar
import adata
from pqdm.processes import pqdm

def get_klines(syms):
    results=pqdm([[_] for _ in syms],adata.stock.market.get_market_min,n_jobs=10,argument_type='args')
    df=pd.concat(results)
    df=df.rename(columns={'stock_code':'symbol','amount':'amt','volume':'qty','trade_time':'time'})
    df['amt']=df['amt'].astype(float)
    df['qty']=df['qty'].astype(float)
    df['time']=pd.to_datetime(df['time'])
    return df

def download_directory(sftp, remote_dir, local_dir):
    """递归下载远程目录到本地"""
    # 创建本地目录
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)
        os.chmod(local_dir, 0o757)
    # 遍历远程目录
    for item in sftp.listdir_attr(remote_dir):
        remote_path = os.path.join(remote_dir, item.filename)
        local_path = os.path.join(local_dir, item.filename)
        if item.st_mode & 0o40000:  # 是目录
            download_directory(sftp, remote_path, local_path)
        else:
            try:
                sftp.get(remote_path, local_path)
                os.chmod(local_path, 0o757)
                # if local_size == remote_size:
                #     print(f"下载完成: {remote_path} -> {local_path}")
                #     return True
                print(f"下载: {remote_path} -> {local_path}")
                # remote_size = sftp.stat(remote_path).st_size
                # local_size = os.path.getsize(lo
                # else:
                #     print(f"下载不完整: 远程大小={remote_size}, 本地大小={local_size}")
            except Exception as e:
                print(f"下载失败 {remote_path}: {str(e)}")

def ftpDownloadRemote(work_date):
    target_path = "/list/10.99.9.109_sftp/daily_after/t0/"
    target_path2 = "/list/10.99.9.109_sftp/daily_after/vwap/"
    dir_name = "parentorder_{}.ldb".format(work_date)
    dir_name2 = "order_{}.ldb".format(work_date)
    local_data_dir = "/home/<USER>/dav/trade_data/zhongtaituoguan/"
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        private_key = paramiko.RSAKey.from_private_key_file('/home/<USER>/.ssh/chaolz2go')
        ssh.connect(hostname='************', port=16888, username='chaolz2go', pkey=private_key)
        sftp = ssh.open_sftp()
        download_directory(sftp, target_path+dir_name, local_data_dir+"t0/"+dir_name)
        download_directory(sftp, target_path+dir_name2, local_data_dir+"t0/"+dir_name2)
        # download_directory(sftp, target_path2+dir_name, "./"+dir_name)
        # download_directory(sftp, target_path2+dir_name2, "./"+dir_name2)
        sftp.close()
        ssh.close()
    except Exception as e:
        print(f"连接失败: {e}")
        return False
    return True



# def read_lvldb_data(p):
#     ldb=leveldb.LevelDB(p)
#     l=[]
#     datas=list(ldb.RangeIter())
#     # true='true'
#     pos=[]
#     params=[]
#     for k,v in datas:
#         d=json.loads(v)
#         pos.append(d) 
#     po_df=pd.DataFrame(pos)
#     ldb=None
#     return po_df 

def read_lvldb_data(p):
    # 1. 打开数据库
    db = plyvel.DB(p, create_if_missing=False)

    # 2. 获取迭代器对象
    iterator = db.iterator()  # 创建迭代器

    # 3. 遍历所有键值对（方法1：手动遍历迭代器）
    pos = []
    for k, v in iterator:  # 直接遍历迭代器（默认从第一个键开始）
        d = json.loads(v.decode('utf-8'))  # 解码 bytes 并解析 JSON
        pos.append(d)

    # 4. 关闭数据库
    db.close()

    # 5. 返回 DataFrame
    return pd.DataFrame(pos)


def open_position(slc):
    p={}
    p['side']=slc['side']
    p['id']=slc['id']
    p['pid']=slc['pid']
    p['price']=slc['filled_price']
    p['qty']=slc['filled_quantity']
    p['sym']=slc['symbol']
    p['time']=slc['last_upd_time']
    p['account_id']=slc['account_id']
    return p

def close_position(p,slc):
    t={}
    t['open_side']=p['side']
    t['open_id']=p['id']
    t['pid']=p['pid']
    t['account_id']=p['account_id']
    t['open_price']=p['price']
    t['sym']=p['sym']
    t['open_time']=p['time']
    t['close_side']=slc['side']
    t['close_id']=slc['id']
    t['close_price']=slc['filled_price']
    t['close_time']=slc['last_upd_time']
    if p['qty']==slc['filled_quantity']:
        t['qty']=p['qty']
        return t,None,None
    if p['qty']>slc['filled_quantity']:
        t['qty']=slc['filled_quantity']
        p['qty']=p['qty']-slc['filled_quantity']
        return t,p,None
    if p['qty']<slc['filled_quantity']:
        t['qty']=p['qty']
        slc['filled_quantity']=slc['filled_quantity']-p['qty']
        return t,None,slc

def get_trds(slcs):
    slcs=slcs[slcs['filled_quantity']>0]
    pos=[]
    trds=[]
    for slc in slcs.to_dict('records'):
        if len(pos)==0:
            pos.append(open_position(slc))
        elif pos[0]['side']==slc['side']:
            pos.append(open_position(slc))
        else:
            for i,p in enumerate(pos) :
                t,p,slc=close_position(p,slc)
                trds.append(t)
                if p is not None:
                    pos[i]=p
                    break
                else:
                    pos[i]=None
                if slc is not None:
                    pos[i]=None
                else:
                    break
            if slc is not None:
                pos.append(open_position(slc))
        pos=[_ for _ in pos if _ is not None]
    return pos,trds

def get_fee_with_position(pos,commission_dict):
    commission=commission_dict.get(pos['account_id'],1.2)/10000
    if pos['side']==1:
        return (pos['price']*pos['qty'])*commission+(pos['midpx']*pos['qty'])*(0.0005+commission)
    else:
        return (pos['midpx']*pos['qty'])*commission+(pos['price']*pos['qty'])*(0.0005+commission)
    
def get_fee_with_trd(trd,commission_dict):
    commission=commission_dict.get(trd['account_id'],1.2)/10000
    if trd['open_side']==1:
        return (trd['open_price']*trd['qty'])*commission + (trd['close_price']*trd['qty'])*(0.0005+commission)
    else:
        return (trd['open_price']*trd['qty'])*(0.0005+commission) + (trd['close_price']*trd['qty'])*commission


def intraday_trading_stats(p,slcs):
    if len(slcs)==0:
        cancel_rate=0
        error_rate=0
    else:
        cancel_rate=len(slcs[slcs['status']=='CANCELED'])/len(slcs)
        error_rate=len(slcs[slcs['status']=='ERROR'])/len(slcs)
    holding_amt=(p['price']*p['quantity']).sum()
    trading_amt=(p['amt_b']+p['amt_s']+p['diff_amt'].abs()).sum()
    profit_without_fee=p['profit'].sum()
    profit_after_fee=p['profit'].sum()-p['fee'].sum()
    holding_ret=profit_after_fee/holding_amt
    trading_ret=profit_after_fee/trading_amt
    position=p['diff_amt'].abs().sum()
    turnover=((p['filledQuantity_b']+p['filledQuantity_s'])*p['price']).sum()/(p['price']*p['quantity']).sum()
    d={'holding_amt':holding_amt,'trading_amt':trading_amt,'profit_without_fee':profit_without_fee,'profit_after_fee':profit_after_fee,'holding_ret':holding_ret,'trading_ret':trading_ret,'turnover':turnover,'cancel_rate':cancel_rate,'error_rate':error_rate,'position':position}
    return d

def stats(pords,slcs,pos,trds):
    if len(slcs)==0:
        cancel_rate=0
        error_rate=0
    else:
        cancel_rate=len(slcs[slcs['status']==110])/len(slcs[slcs['status']!=120])
        error_rate=len(slcs[slcs['status']==120])/len(slcs)
    holding_amt=(pords['price']*pords['quantity']).sum()
    trading_amt=slcs['amt'].sum()
    profit_without_fee=trds['profit'].sum()+pos['profit'].sum()
    profit_after_fee=(trds['profit'].sum()-trds['fee'].sum())+(pos['profit'].sum()-pos['fee'].sum())
    position=(pos['price']*pos['qty']).sum()
    holding_ret=profit_after_fee/holding_amt
    trading_ret=profit_after_fee/trading_amt
    turnover=trading_amt/holding_amt
    win_ratio_before_fee=len(trds[(trds['profit'])>0])/len(trds)
    win_ratio_after_fee= len(trds[(trds['profit']-trds['fee'])>0])/len(trds)
    d={'holding_amt':holding_amt,'trading_amt':trading_amt,'profit_without_fee':profit_without_fee,'profit_after_fee':profit_after_fee,'holding_ret':holding_ret,'trading_ret':trading_ret,'turnover':turnover,'cancel_rate':cancel_rate,'error_rate':error_rate,'position':position,'win_ratio_before_fee':win_ratio_before_fee,'win_ratio_after_fee':win_ratio_after_fee}
    return d

def pct_data_process(x):
    return round(x*100,1)

def display(l):
    df=pd.DataFrame(l)
    df['holding_ret']=df['holding_ret']*10000
    df['trading_ret']=df['trading_ret']*10000
    df['turnover']=df['turnover'].apply(pct_data_process)
    df['error_rate']=df['error_rate'].apply(pct_data_process)
    df['cancel_rate']=df['cancel_rate'].apply(pct_data_process)
    df['win_ratio_before_fee']=df['win_ratio_before_fee'].apply(pct_data_process)
    df['win_ratio_after_fee']=df['win_ratio_after_fee'].apply(pct_data_process)
    df = df.apply(lambda x: f"{x:.2f}" if isinstance(x, float) else x)
    df=df.rename(columns={'holding_amt':'持仓金额(元)', 'trading_amt':'交易金额(元)','profit_without_fee':'费前总收益(元)','profit_after_fee':'费后总收益(元)','holding_ret':'底仓收益率(bps)','trading_ret':'开仓收益率(bps)','turnover':'换手%','cancel_rate':'撤单率%','error_rate':'废单率%','win_ratio_before_fee':'费前胜率%','win_ratio_after_fee':'费后胜率%','position':'未平仓位(元)','group_name':'分组'})
    return df


if __name__=="__main__":
    
    date="20250624"
    prev_date=trading_calendar.get_prev_trade_date(date)
    ftpDownloadRemote(date)
    commission_dict={'109156033251':1.2}
    
    local_data_dir = "/home/<USER>/dav/trade_data/zhongtaituoguan/"
    pords=read_lvldb_data(os.path.join(local_data_dir, "t0", "parentorder_{}.ldb".format(date)))
    slcs=read_lvldb_data(os.path.join(local_data_dir, "t0", "order_{}.ldb".format(date)))
    pxs=data_reader.get_close_pxs_by_date_from_file(prev_date)
    slcs['side']=slcs['operation'].apply(lambda x:-1 if x==110 else 1)
    slcs['amt']=slcs['filled_price']*slcs['filled_quantity']
    pords['price']=pords['symbol'].apply(lambda x:pxs.get(x,0))
    slcs=slcs.sort_values('create_time')
    poslist=[]
    trdlist=[]
    for pid,g in slcs.groupby('pid'):
        pos,trds=get_trds(g)
        if len(pos)>0:
            poslist=poslist+pos
        if len(trds)>0:
            trdlist=trdlist+trds
    pos=pd.DataFrame(poslist)
    trds=pd.DataFrame(trdlist)
    mktpx={_['stock_code']:float(_['price']) for _ in  adata.stock.market.list_market_current(code_list=pos['sym'].unique()).to_dict('records')}
    pos['midpx']=pos['sym'].apply(lambda x:mktpx.get(x,0))
    pos['profit']=(pos['price']-pos['midpx'])*pos['qty']*-pos['side']
    pos['fee']=pos.apply(lambda x:get_fee_with_position(x,commission_dict),axis=1)
    trds['fee']=trds.apply(lambda x:get_fee_with_trd(x,commission_dict),axis=1)
    trds['profit']=(trds['open_price']-trds['close_price'])*trds['qty']*-trds['open_side']
    l=[]
    ret=stats(pords,slcs,pos,trds)
    ret['group_name']='total'
    l.append(ret)
    for name,g in pords.groupby('account_id'):
        ret=stats(g,slcs[slcs['account_id']==name],pos[pos['account_id']==name],trds[trds['account_id']==name])
        ret['group_name']=name
        l.append(ret)
    for name,g in trds.groupby('open_side'):
        ret=stats(pords[pords['id'].isin(g['pid'])],slcs[slcs['pid'].isin(g['pid'])],pos[pos['pid'].isin(g['pid'])],g)
        ret['group_name']='long' if name==1 else 'short'
        l.append(ret)
    result = display(l)
    print(result)
 
    